# Linux_Plan_Min - 动态任务图谱差分更新实验

## 项目概述

本项目实现了"动态任务图谱差分更新"实验，专门针对Breakfast数据集中的"cereals"任务。该实验通过差分学习机制动态调整任务图谱的边权重，以提高动作序列预测的准确性。

## 核心特性

- **动态图更新**: 基于当前帧特征与动作原型的差异，实时调整任务图权重
- **差分学习**: 使用MLP网络学习权重调整策略
- **PyTorch Lightning**: 基于Lightning框架的模块化训练流程
- **Hydra配置**: 灵活的配置管理系统
- **完整评估**: 支持IoU和编辑距离等多种评估指标

## 项目结构

```
Plan_Min/
├── src/                    # 源代码
│   ├── core/              # 核心算法模块
│   │   ├── diff_update.py     # 差分更新MLP
│   │   ├── task_graph.py      # 动态任务图
│   │   └── lightning_module.py # Lightning训练模块
│   ├── data/              # 数据处理模块
│   │   └── datamodule.py      # 数据加载和预处理
│   ├── pipeline/          # 主程序
│   │   └── train.py           # 训练主程序
│   └── utils/             # 工具模块
│       ├── metrics.py         # 评估指标
│       └── stats_computer.py  # 统计计算
├── configs/               # 配置文件
│   └── config.yaml           # 主配置文件
├── stats/                 # 统计数据存储
├── outputs/               # 实验输出
├── requirements.txt       # 依赖列表
├── Agent.md              # 技术规范文档
└── README.md             # 项目说明

```

## 环境要求

- Python 3.8+
- PyTorch 1.12+
- PyTorch Lightning 1.8+
- Hydra Core 1.2+

## 安装依赖

```bash
pip install -r requirements.txt
```

## 数据集说明

本项目使用Breakfast数据集的cereals任务：

- **特征文件**: `breakfast_data/s{1,2,3,4}/cereals/*.npy` 或 `*.txt`
- **标签文件**: `segmentation_coarse/s{1,2,3,4}_label/cereals/*.txt`
- **动作类别**: 48个动作类别（从1-48映射到0-47）

**注意**: 数据集存储在远程服务器上，本地环境可能无法访问。代码已设计为在数据不可用时使用模拟数据进行测试。

## 使用方法

### 基本训练

```bash
cd Plan_Min
python src/pipeline/train.py
```

### 自定义配置

```bash
# 修改批次大小
python src/pipeline/train.py trainer.batch_size=32

# 修改学习率
python src/pipeline/train.py model.lr=1e-3

# 设置输出目录
export OUTPUT_DIR=/path/to/output
python src/pipeline/train.py
```

### 调试模式

```bash
# 快速开发运行
python src/pipeline/train.py debug.fast_dev_run=true

# 限制训练数据
python src/pipeline/train.py debug.limit_train_batches=0.1
```

## 核心算法

### 1. 差分特征计算

```
Diff_k = |V_k - V_n_k|
```

其中 V_k 是当前帧特征，V_n_k 是对应动作的原型特征。

### 2. 权重调整预测

```
ΔW_k = α * tanh(MLP(Diff_k))
```

通过两层MLP网络预测权重调整量，输出范围限制在(-α, α)。

### 3. 动态图更新

- **训练时**: `logits_k = W0[n_k] + ΔW_k` (非累积)
- **推理时**: `W[n_k] += ΔW_k` (累积更新)

## 评估指标

- **IoU**: 基于段集合的交并比
- **编辑距离**: 归一化的Levenshtein距离
- **段级评估**: 将帧级序列合并为段级序列后评估

## 配置说明

主要配置参数（`configs/config.yaml`）：

```yaml
model:
  D: 64        # 特征维度
  M: 48        # 动作类别数
  H: 256       # MLP隐藏层维度
  alpha: 0.05  # 权重调整缩放因子
  lr: 1e-4     # 学习率

trainer:
  max_epochs: 50
  batch_size: 16
  accelerator: 'auto'
  devices: 1
```

## 实验输出

训练完成后，将在输出目录生成：

- 训练日志和检查点
- 验证指标曲线
- 模型权重文件
- Hydra运行配置

## 故障排除

### 1. 数据集不可用

如果看到数据路径不存在的警告，这是正常的。代码会自动创建模拟统计数据继续运行。

### 2. 特征维度不匹配

代码会自动检测实际特征维度并更新配置，无需手动调整。

### 3. 内存不足

可以减少批次大小：

```bash
python src/pipeline/train.py trainer.batch_size=8
```

## 技术细节

详细的技术规范和数学公式请参考 `Agent.md` 文件。

## 开发状态

- ✅ 核心算法实现完成
- ✅ 数据处理模块完成
- ✅ 训练流程完成
- ✅ 评估指标完成
- ✅ 单元测试通过
- ⚠️ 需要实际数据集进行完整验证

## 许可证

本项目仅用于学术研究目的。
